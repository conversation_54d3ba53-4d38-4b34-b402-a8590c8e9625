import { Injectable } from "@angular/core";
import { CommonResponseWrapper } from '../../../models';
import { Tam4HttpClientService } from "../../../providers";
import { ValidateDataResponse } from '../../materials-editor/models/material-editor.types';
import {
    ClientStatuses,
    CreateDeduplicationRequest,
    CreateDeduplicationRequestBE, DeduplicationBasicDataEnrichmentRequest,
    DeduplicationMaterialData,
    DeduplicationRequest,
    DeduplicationValidateDraftResponse, DeduplicationValidateMaterialRequest,
    MaterialIdsRequest,
    SubtypesRelationships
} from '../models/deduplication.models';

const RELATIONSHIP_BASE_URL = `/relationships/api`;
const MATERIALS_URL = `materials/api`;
const WORKLIST_URL = `/worklists/api`;
const DEDUPLICATION_URL = `${RELATIONSHIP_BASE_URL}/deduplication`;

const URLS = {
  GET_SUBTYPES_RELATIONSHIPS: `${RELATIONSHIP_BASE_URL}/get-all-relationship-subtypes`,
  VALIDATE_CREATION: `${RELATIONSHIP_BASE_URL}/validate-creation`,
  VALIDATE_MATERIAL: `${DEDUPLICATION_URL}/validate/material`,
  GET_ENRICHMENT_MATERIAL_DETAILS: `${DEDUPLICATION_URL}/get-enrichment-details`,
  GET_ENRICHMENT_PLANT_DETAILS: `${DEDUPLICATION_URL}/get-plant-extension-data`,
  PLANT_ENRICHMENT_SETUP: `${DEDUPLICATION_URL}/setup`,
  GET_MASTER_DATA_STATUS: `${MATERIALS_URL}/deduplication-master-data-status-definitions-by-client`,
  GET_ADDITIONAL_INFO: `${WORKLIST_URL}/relationship-creation/process/additional-info`,
  SEND_DEDUPLICATION_REQUEST: `${DEDUPLICATION_URL}`
};

@Injectable({
    providedIn: 'root'
})
export class DeduplicationDao {

    constructor(private http: Tam4HttpClientService) {

    }

    getRelationshipCreationMaterialsDetails(requestBody: MaterialIdsRequest,
                                            callbackOk: (resp: DeduplicationMaterialData) => void,
                                            callbackKo: (err: any) => void) {
                this.http.post<any>(
                    "/materials/api/details/relationship-creation", // TODO: Fix this
                    requestBody,
                    (resp: DeduplicationMaterialData) => {
                        callbackOk(resp);
                    },
                    {},
                    false,
                    callbackKo
                );
    }

    getSubtypesRelationships(
                      callbackOk: (resp: SubtypesRelationships[]) => void,
                      callbackKo: (err: any) => void) {
                        this.http.get<SubtypesRelationships[]>(
                URLS.GET_SUBTYPES_RELATIONSHIPS,
                {},
                (resp: SubtypesRelationships[]) => { callbackOk(resp); },
                {},
                false,
                callbackKo
            );
    }

    validateRelationship_(requestBody: CreateDeduplicationRequest,
                          callbackOk: (resp: DeduplicationValidateDraftResponse) => void,
                          callbackKo: (err: any) => void) {
                          this.http.get<DeduplicationValidateDraftResponse>(
                URLS.VALIDATE_CREATION,
                {},
                (resp: DeduplicationValidateDraftResponse) => { callbackOk(resp); },
                {},
                false,
                callbackKo
            );
    }

    validateRelationship(requestBody: CreateDeduplicationRequestBE,
                         callbackOk: (resp: DeduplicationValidateDraftResponse) => void,
                         callbackKo: (err: any) => void) {
        this.http.post<DeduplicationValidateDraftResponse>(
            URLS.VALIDATE_CREATION,
            requestBody,
            (resp: DeduplicationValidateDraftResponse) => { callbackOk(resp); },
            {},
            false,
            callbackKo
        );
    }

    getEnrichmentMaterialDetails(request: DeduplicationBasicDataEnrichmentRequest,
                                 callbackOk: (resp: CommonResponseWrapper<any>) => void,
                                 callbackKo: (err: any) => void) {
        this.http.post<CommonResponseWrapper<any>>(`${URLS.GET_ENRICHMENT_MATERIAL_DETAILS}`,
            request,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    getEnrichmentPlantDetails(request: DeduplicationBasicDataEnrichmentRequest,
                              callbackOk: (resp: CommonResponseWrapper<any>) => void,
                              callbackKo: (err: any) => void) {
        this.http.post<CommonResponseWrapper<any>>(`${URLS.GET_ENRICHMENT_PLANT_DETAILS}`,
            request,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    plantEnrichmentSetup(request: any,
                         callbackOk: (resp: CommonResponseWrapper<any>) => void,
                         callbackKo: (err: any) => void) {
        this.http.post<CommonResponseWrapper<any>>(`${URLS.PLANT_ENRICHMENT_SETUP}`,
            request,
            (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }


    getMaterialStatusValuesByClient(clients: string[], language: string, fallbackLanguages: string[],
                                    callbackOk: (resp: CommonResponseWrapper<ClientStatuses>) => void,
                                    callbackKo: (err: any) => void) {
        const requestBody = {
          clients,
          language,
          size: 1000,
          page: 1,
          fallbackLanguages
        };
        this.http.post<CommonResponseWrapper<any>>(`${URLS.GET_MASTER_DATA_STATUS}`, requestBody,
          (resp: CommonResponseWrapper<any>) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
      }

    sendRelationshipRequest(requestBody: DeduplicationRequest,
                            callbackOk: (resp: any) => void,
                            callbackKo: (err: any) => void) {
        this.http.post<any>(`${URLS.SEND_DEDUPLICATION_REQUEST}`,
            requestBody,
            (resp: any) => {
                callbackOk(resp);
            },
            {}, false, callbackKo);
    }

    sendAdditionalEnrichmentRequest(requestBody: any,
                                    callbackOk: (resp: any) => void,
                                    callbackKo: (err: any) => void) {
                this.http.post<any>(
                    `${URLS.GET_ADDITIONAL_INFO}`,
                    requestBody,
                    (resp: any) => {
                        callbackOk(resp);
                    },
                    {},
                    false,
                    callbackKo
                );
    }

    validateMaterial(requestBody: DeduplicationValidateMaterialRequest,
                     callbackOk: (resp: CommonResponseWrapper<ValidateDataResponse>) => void,
                     callbackKo: (err: any) => void) {
        this.http.post<CommonResponseWrapper<ValidateDataResponse>>(
            URLS.VALIDATE_MATERIAL,
            requestBody,
            (resp: CommonResponseWrapper<ValidateDataResponse>) => {
                callbackOk(resp);
            },
            {},
            false,
            callbackKo
        );
    }


}
